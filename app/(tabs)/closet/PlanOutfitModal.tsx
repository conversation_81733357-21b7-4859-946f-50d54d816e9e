import React, { useState, useCallback, useEffect } from 'react';
// Haptic feedback will be implemented when the library is available
// import * as Haptics from 'expo-haptics';
import { View, TouchableOpacity, FlatList, ActivityIndicator, Alert, BackHandler, Platform } from 'react-native';
import Modal from 'react-native-modal';
import { Search, ChevronDown } from 'lucide-react-native';
import DatePickerModal from '@/components/DatePickerModal';
import { GenericSuccessModal } from '@/components/GenericSuccessModal';
import Meteor from '@meteorrn/core';
import { useQuery } from '@tanstack/react-query';
import { getClothes } from '@/methods/cloths';
// import { generateCollage } from './utils/collageGenerator'; // Removed for now - will be added back when collage feature is implemented
import { createOutfit, updateOutfit, convertLocationToCoordinates } from '@/methods/outfits';
import { Checkbox } from '@/components/common/Checkbox';
// Import API-based categories
import { fetchCategoriesFromBackend } from '@/data/gender-categories';
import { getCurrentUserGender } from '@/data/categories';
import { getUserProfile } from '@/methods/users';
import useResponsive from '@/hooks/useResponsive';
import {
  ModalContainer,
  ContentContainer,
  FrameContainer,
  FormGroup,
  InputFieldContainer,
  InputFieldLabel,
  StyledTextInput,
  AndroidLocationTextInput,
  DateButton,
  DateButtonText,
  SectionTitle,
  DropdownContainer,
  DropdownText,
  SearchContainer,
  SearchInput,
  FilterContainer,
  FilterButtonsContainer,
  FilterButton,
  FilterButtonText,
  ClothesGridContainer,
  ClothesItemContainer,
  ClothesItemImage,
  ModalHeader,
  HeaderButton,
  HeaderButtonText,
  HeaderTitle,
  BarIndicatorContainer,
  BarIndicator,
  ClothItemName
} from './PlanOutfitModal.styles';

interface PlanOutfitModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSave: (outfitData: OutfitData) => void;
  editMode?: boolean;
  existingOutfit?: any;
}

interface OutfitData {
  name: string;
  location: string;
  eventDate: Date;
  packingListId?: string;
  selectedItems: string[];
  collageUri?: string;
}

interface PackingList {
  _id: string;
  name: string;
}

function PlanOutfitModal({ isVisible, onClose, onSave, editMode = false, existingOutfit }: PlanOutfitModalProps) {
  // Get responsive values
  const { isTablet } = useResponsive();

  // Handle back button press on Android
  useEffect(() => {
    if (Platform.OS === 'android') {
      const backAction = () => {
        if (isVisible) {
          onClose();
          return true; // Prevent default behavior
        }
        return false; // Allow default behavior
      };

      const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

      return () => {
        if (backHandler) {
          backHandler.remove();
        }
      };
    }
  }, [isVisible, onClose]);

  // Form state
  const [outfitName, setOutfitName] = useState('Option 1');
  const [location, setLocation] = useState('London, United Kingdom');
  const [eventDate, setEventDate] = useState(new Date());
  const [selectedPackingList, setSelectedPackingList] = useState<PackingList>({ _id: 'none', name: 'None (No Trip)' });
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // Image loading states
  const [imageLoadingStates, setImageLoadingStates] = useState<{[key: string]: boolean}>({});

  // Modal states
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showPackingListDropdown, setShowPackingListDropdown] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successModalData, setSuccessModalData] = useState({ outfitName: '', itemCount: 0, isUpdate: false });

  // State for gender-specific categories
  const [displayCategories, setDisplayCategories] = useState<string[]>(['All', 'Tops', 'Dresses', 'Bottoms', 'Shoes', 'Accessories']);

  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();

  // Use the standardized getClothes function
  const { data: clothesData = { items: [] }, isLoading: isLoadingClothes } = getClothes();

  // Initialize the outfit mutations
  const createOutfitMutation = createOutfit();
  const updateOutfitMutation = updateOutfit();

  // Extract items from the standardized response
  const clothesItems = clothesData?.items || [];

  // Pre-populate form when in edit mode
  React.useEffect(() => {
    if (editMode && existingOutfit && isVisible) {
      console.log('Pre-populating form for edit mode:', existingOutfit);

      // Set form fields from existing outfit data
      setOutfitName(existingOutfit.name || 'Option 1');
      setLocation(existingOutfit.location || 'London, United Kingdom');

      // Handle date conversion
      if (existingOutfit.eventDate) {
        const eventDate = existingOutfit.eventDate instanceof Date
          ? existingOutfit.eventDate
          : new Date(existingOutfit.eventDate);
        setEventDate(eventDate);
      }

      // Set selected items
      if (existingOutfit.selectedItems && Array.isArray(existingOutfit.selectedItems)) {
        setSelectedItems(existingOutfit.selectedItems);
      } else if (existingOutfit.itemIds && Array.isArray(existingOutfit.itemIds)) {
        // Handle backend format where items are in itemIds
        setSelectedItems(existingOutfit.itemIds);
      }

      // Handle packing list/trip selection
      if (existingOutfit.packingListId) {
        // Find the matching trip in packingLists when they're loaded
        // This will be handled in a separate useEffect when packingLists are available
      }

      console.log('Form pre-populated for editing outfit:', existingOutfit.name);
    } else if (!editMode && isVisible) {
      // Reset form when opening in create mode
      resetForm();
    }
  }, [editMode, existingOutfit, isVisible]);

  // Initialize data processing without excessive logging
  React.useEffect(() => {
    // Process clothes data silently
  }, [clothesItems]);

  // Fetch packing lists from the backend
  const { data: tripsData, isLoading: isLoadingTrips } = useQuery({
    queryKey: ['trips'],
    queryFn: () => {
      return new Promise<any>((resolve, reject) => {
        Meteor.call('events-fetchAll', {}, (error: any, response: any) => {
          if (error) {
            console.error('Error fetching trips:', error);
            reject(error);
          } else {
            console.log('Trips API response:', response);
            resolve(response);
          }
        });
      });
    },
    select: (data: any) => {
      // Extract trips from the nested data structure
      return data?.data?.events || [];
    }
  });

  // Check if a trip is in the future (has an end date that is today or later)
  const isFutureTrip = (trip: any): boolean => {
    if (!trip.endDate) return false;

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tripEnd = new Date(trip.endDate);
    tripEnd.setHours(0, 0, 0, 0);

    return tripEnd >= today;
  };

  // Format trips as packing lists with dates, filtering out past trips
  const packingLists = [
    // Add a "None" option as the first item
    { _id: 'none', name: 'None (No Trip)' },
    // Add actual trips with their date information, filtering out past trips
    ...(tripsData?.map((trip: any) => ({
      _id: trip._id,
      name: trip.name || 'Unnamed Trip',
      startDate: trip.startDate ? new Date(trip.startDate) : undefined,
      endDate: trip.endDate ? new Date(trip.endDate) : undefined
    }))
    .filter(trip => isFutureTrip(trip)) || [
      // Fallback to hardcoded values if no trips are available
      { _id: 'trip1', name: 'US Trip' },
      { _id: 'trip2', name: 'Europe Vacation' },
      { _id: 'trip3', name: 'Beach Getaway' },
      { _id: 'trip4', name: 'Business Trip' }
    ])
  ];

  // Load categories from backend API
  React.useEffect(() => {
    const loadGenderCategories = async () => {
      try {
        // Get gender from user profile
        let gender = null;
        if (userProfile?.data?.profile?.gender) {
          gender = userProfile.data.profile.gender;
          console.log('PlanOutfitModal - Using gender from userProfile:', gender);
        } else {
          // Try to get gender from Meteor.user()
          gender = getCurrentUserGender();
          console.log('PlanOutfitModal - Using gender from getCurrentUserGender:', gender);
        }

        if (!gender) {
          console.warn('PlanOutfitModal - No gender available, using default categories');
          setDisplayCategories(['All', 'Tops', 'Bottoms', 'Shoes', 'Accessories']);
          return;
        }

        // Get categories from backend based on gender
        const backendCategories = await fetchCategoriesFromBackend(gender);

        if (backendCategories && backendCategories.length > 0) {
          // Format categories for display - extract just the top-level category names
          const formattedCategories = [
            'All', // Always include "All" category
            ...backendCategories.map(category => category.name)
          ];

          console.log(`PlanOutfitModal - Loaded ${formattedCategories.length} categories from backend for gender: ${gender}`);
          setDisplayCategories(formattedCategories);
        } else {
          console.warn('PlanOutfitModal - No categories returned from backend');
          // Fallback to default categories if no categories returned
          setDisplayCategories(['All', 'Tops', 'Bottoms', 'Shoes', 'Accessories']);
        }
      } catch (error) {
        console.error('Error loading categories from backend:', error);
        // Fallback to default categories if there's an error
        setDisplayCategories(['All', 'Tops', 'Bottoms', 'Shoes', 'Accessories']);
      }
    };

    loadGenderCategories();
  }, [userProfile]);

  // Debug logging removed to prevent excessive console output

  // Auto-select trip based on event date
  React.useEffect(() => {
    // Only run this if we have trips data and the user hasn't manually selected a trip yet
    if (tripsData && tripsData.length > 0 && (!selectedPackingList || selectedPackingList._id === 'none')) {
      const matchingTrip = findTripForDate(eventDate, packingLists);

      if (matchingTrip) {
        console.log(`Auto-selected trip "${matchingTrip.name}" for date ${formatDate(eventDate)}`);
        setSelectedPackingList(matchingTrip);
      } else {
        // If no matching trip is found, select "None"
        setSelectedPackingList(packingLists[0]);
      }
    }
  }, [eventDate, tripsData]);

  // Filter clothes based on search query and selected category
  const filteredClothes = clothesItems.filter((item: any) => {
    // Check if the item has a name and it matches the search query
    const matchesSearch = item.name?.toLowerCase().includes(searchQuery.toLowerCase());
    if (!matchesSearch) {
      return false;
    }

    // If "All" category is selected, show all items that match the search
    if (selectedCategory === 'All') {
      return true;
    }

    // Get the category name from the item
    const itemCategory = item.category?.name || '';

    // Skip items without a category
    if (!itemCategory) {
      return false;
    }

    // Normalize categories for comparison
    const normalizedItemCategory = itemCategory.toLowerCase().trim();
    const normalizedFilterCategory = selectedCategory.toLowerCase().trim();

    // Exact match (case-insensitive)
    if (normalizedItemCategory === normalizedFilterCategory) {
      return true;
    }

    // Handle plural/singular variations
    if (normalizedItemCategory.endsWith('s') &&
        normalizedItemCategory.slice(0, -1) === normalizedFilterCategory) {
      // Item is plural, filter is singular (e.g., "Tops" matches "Top")
      return true;
    }

    if (normalizedFilterCategory.endsWith('s') &&
        normalizedFilterCategory.slice(0, -1) === normalizedItemCategory) {
      // Filter is plural, item is singular (e.g., "Top" matches "Tops")
      return true;
    }

    // No match
    return false;
  });

  // Toggle item selection with haptic feedback
  const toggleItemSelection = useCallback((itemId: string) => {
    // Haptic feedback will be implemented when the library is available
    // Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
    } else {
      setSelectedItems([...selectedItems, itemId]);
    }
  }, [selectedItems]);

  // Handle save with haptic feedback - supports both create and update
  const handleSave = async () => {
    // Haptic feedback will be implemented when the library is available
    // Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

    // Prevent multiple submissions
    const isLoading = editMode ? updateOutfitMutation.isPending : createOutfitMutation.isPending;
    if (isLoading) {
      return;
    }

    try {
      // Check if any items are selected
      if (selectedItems.length === 0) {
        Alert.alert('No items selected', 'Please select at least one clothing item for your outfit.');
        return;
      }

      // Convert location string to coordinates
      const locationWithCoordinates = await convertLocationToCoordinates(location);
      console.log('Location converted to coordinates:', locationWithCoordinates);

      if (editMode && existingOutfit) {
        // Update existing outfit
        console.log('Updating outfit with backend API...');

        const updateParams = {
          outfitId: existingOutfit._id,
          name: outfitName,
          location: locationWithCoordinates,
          eventDate: eventDate,
          itemIds: selectedItems,
          plannedDate: eventDate,
        };

        // Call the backend API to update the outfit
        await updateOutfitMutation.mutateAsync(updateParams);

        // Capture data for success modal before any state changes
        setSuccessModalData({
          outfitName: outfitName,
          itemCount: selectedItems.length,
          isUpdate: true
        });
        setShowSuccessModal(true);
      } else {
        // Create new outfit
        const createParams = {
          name: outfitName,
          location: locationWithCoordinates,
          eventDate: eventDate,
          itemIds: selectedItems,
          plannedDate: eventDate,
        };

        // Call the backend API to create the outfit
        await createOutfitMutation.mutateAsync(createParams);

        // Capture data for success modal before any state changes
        setSuccessModalData({
          outfitName: outfitName,
          itemCount: selectedItems.length,
          isUpdate: false
        });
        setShowSuccessModal(true);
      }

      // Reset form and close modal (only for edit mode, new outfits will close after success modal)
      if (editMode) {
        onClose();
      } else {
        resetForm();
      }

      // Call the original onSave callback for any additional UI updates
      // Pass the data in the expected format for backward compatibility
      const outfitData: OutfitData = {
        name: outfitName,
        location,
        eventDate,
        packingListId: selectedPackingList && selectedPackingList._id !== 'none' ? selectedPackingList._id : undefined,
        selectedItems,
        collageUri: undefined // Collage generation removed for now
      };
      onSave(outfitData);

    } catch (error) {
      console.error(`Error ${editMode ? 'updating' : 'creating'} outfit:`, error);
      Alert.alert(
        'Error',
        `Failed to ${editMode ? 'update' : 'create'} outfit. Please try again.`,
        [{ text: 'OK' }]
      );
    }
  };

  // Reset form
  const resetForm = () => {
    setOutfitName('Option 1');
    setLocation('London, United Kingdom');
    setEventDate(new Date());
    setSelectedPackingList({ _id: 'none', name: 'None (No Trip)' });
    setSearchQuery('');
    setSelectedCategory('All');
    setSelectedItems([]);
  };

  // Format date for display
  const formatDate = (date: Date): string => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return `${months[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`;
  };

  // Check if a date falls within a trip's date range
  const isDateWithinTrip = (date: Date, trip: any): boolean => {
    if (!trip.startDate || !trip.endDate) return false;

    const tripStart = new Date(trip.startDate);
    const tripEnd = new Date(trip.endDate);

    // Set time to midnight for accurate date comparison
    const compareDate = new Date(date);
    compareDate.setHours(0, 0, 0, 0);
    tripStart.setHours(0, 0, 0, 0);
    tripEnd.setHours(0, 0, 0, 0);

    return compareDate >= tripStart && compareDate <= tripEnd;
  };

  // Find a trip that matches the selected date
  const findTripForDate = (date: Date, trips: any[]): any => {
    // Skip the first item which is the "None" option
    const actualTrips = trips.slice(1);
    return actualTrips.find(trip => isDateWithinTrip(date, trip)) || null;
  };

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      style={{ margin: 0, justifyContent: 'flex-end', flex: 1 }}
      swipeDirection="down"
      onSwipeComplete={onClose}
      backdropOpacity={0.7}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      animationInTiming={250}
      animationOutTiming={200}
      backdropTransitionInTiming={250}
      backdropTransitionOutTiming={200}
      useNativeDriver={true}
      useNativeDriverForBackdrop={true}
      hideModalContentWhileAnimating={true}
      propagateSwipe={true}
      avoidKeyboard={true}
      onModalHide={onClose}
    >
      <ModalContainer>
        <BarIndicatorContainer>
          <BarIndicator />
        </BarIndicatorContainer>

        <ModalHeader>
          <HeaderButton onPress={onClose}>
            <HeaderButtonText>Cancel</HeaderButtonText>
          </HeaderButton>
          <HeaderTitle>{editMode ? 'Edit Outfit' : 'Plan Outfit'}</HeaderTitle>
          <HeaderButton onPress={handleSave} disabled={editMode ? updateOutfitMutation.isPending : createOutfitMutation.isPending}>
            {(editMode ? updateOutfitMutation.isPending : createOutfitMutation.isPending) ? (
              <ActivityIndicator size="small" color="#0E7E61" />
            ) : (
              <HeaderButtonText>{editMode ? 'Update' : 'Save'}</HeaderButtonText>
            )}
          </HeaderButton>
        </ModalHeader>

        <ContentContainer
          showsVerticalScrollIndicator={true}
          bounces={true}
          contentContainerStyle={{
            paddingBottom: isTablet ? 40 : 20,
            flexGrow: 1
          }}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="on-drag"
          scrollEventThrottle={16}
        >
          <FrameContainer>
            <FormGroup>
              {/* Outfit Name Input */}
              <InputFieldContainer>
                <InputFieldLabel>Outfit Name</InputFieldLabel>
                <StyledTextInput
                  value={outfitName}
                  onChangeText={setOutfitName}
                />
              </InputFieldContainer>

              {/* Location Input */}
              <InputFieldContainer>
                <InputFieldLabel>Location</InputFieldLabel>
                <AndroidLocationTextInput
                  value={location}
                  onChangeText={setLocation}
                />
              </InputFieldContainer>

              {/* Event Date Input */}
              <InputFieldContainer>
                <InputFieldLabel>Event Date</InputFieldLabel>
                <DateButton onPress={() => setShowDatePicker(true)}>
                  <DateButtonText>{formatDate(eventDate)}</DateButtonText>
                </DateButton>
              </InputFieldContainer>
            </FormGroup>

            {/* Add to Packing List Section */}
            <View style={{ marginTop: isTablet ? 24 : 16 }}>
              <SectionTitle>Add to packing list (optional)</SectionTitle>
              <DropdownContainer
                style={{ marginTop: isTablet ? 4 : 2 }}
                onPress={() => {
                if (!isLoadingTrips) {
                  setShowPackingListDropdown(true);
                }
              }}>
                <DropdownText>
                  {isLoadingTrips
                    ? 'Loading trips...'
                    : selectedPackingList
                      ? selectedPackingList.name
                      : packingLists.length > 0
                        ? packingLists[0].name
                        : 'No trips available'}
                </DropdownText>
                {isLoadingTrips
                  ? <ActivityIndicator size="small" color="#0E7E61" />
                  : <ChevronDown size={18} color="#333333" />}
              </DropdownContainer>
            </View>

            {/* Search Input */}
            <SearchContainer>
              <Search size={16} color="#767676" />
              <SearchInput
                placeholder="Search"
                value={searchQuery}
                onChangeText={setSearchQuery}
                placeholderTextColor="#767676"
              />
            </SearchContainer>

            {/* Filter Categories */}
            <FilterContainer horizontal showsHorizontalScrollIndicator={false}>
              <FilterButtonsContainer>
                {displayCategories.map((category) => (
                  <FilterButton
                    key={category}
                    active={selectedCategory === category}
                    onPress={() => {
                    // Haptic feedback will be implemented when the library is available
                    // Haptics.selectionAsync();
                    setSelectedCategory(category);
                  }}
                  >
                    <FilterButtonText active={selectedCategory === category}>
                      {category}
                    </FilterButtonText>
                  </FilterButton>
                ))}
              </FilterButtonsContainer>
            </FilterContainer>

            {/* Clothes Grid */}
            <ClothesGridContainer>
              {filteredClothes.map((item: any) => (
                <ClothesItemContainer key={item._id} onPress={() => toggleItemSelection(item._id)}>
                  {/* Display image with proper error handling */}
                  <ClothesItemImage
                    source={item.imageUrl
                      ? { uri: item.imageUrl }
                      : require('../../../assets/images/placeholder-item.png')}
                    resizeMode="cover"
                    onLoadStart={() => {
                      // Set loading state and add a small delay to make spinner visible
                      setImageLoadingStates(prev => ({ ...prev, [item._id]: true }));
                    }}
                    onLoadEnd={() => {
                      // Add a small delay before hiding the spinner (for testing)
                      setTimeout(() => {
                        setImageLoadingStates(prev => ({ ...prev, [item._id]: false }));
                      }, 1000);
                    }}
                    onError={(e: any) => {
                      console.error(`Image error for ${item.name}:`, e.nativeEvent?.error || 'Unknown error');
                      setImageLoadingStates(prev => ({ ...prev, [item._id]: false }));
                    }}
                  />

                  {/* Item name below the image */}
                  <ClothItemName numberOfLines={1}>{item.name}</ClothItemName>

                  {(isLoadingClothes || imageLoadingStates[item._id]) && (
                    <View style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      backgroundColor: 'rgba(240, 240, 240, 0.7)',
                      justifyContent: 'center',
                      alignItems: 'center',
                      borderRadius: 8
                    }}>
                      <ActivityIndicator
                        size="large"
                        color="#0E7E61"
                      />
                    </View>
                  )}
                  <Checkbox
                    checked={selectedItems.includes(item._id)}
                    onToggle={() => toggleItemSelection(item._id)}
                    size={isTablet ? 28 : 24}
                    containerStyle={{
                      position: 'absolute',
                      bottom: isTablet ? 12 : 8,
                      right: isTablet ? 12 : 8
                    }}
                  />
                </ClothesItemContainer>
              ))}
            </ClothesGridContainer>
          </FrameContainer>
        </ContentContainer>
      </ModalContainer>

      {/* Date Picker Modal */}
      <DatePickerModal
        visible={showDatePicker}
        onClose={() => setShowDatePicker(false)}
        onDateChange={setEventDate}
        value={eventDate}
        title="Select Event Date"
      />

      {/* Packing List Dropdown (simplified implementation) */}
      {showPackingListDropdown && (
        <Modal
          isVisible={showPackingListDropdown}
          onBackdropPress={() => setShowPackingListDropdown(false)}
          style={{ margin: 20, justifyContent: 'center', alignItems: 'center' }}
          backdropOpacity={0.5}
        >
          <View style={{
            backgroundColor: 'white',
            borderRadius: isTablet ? 20 : 16,
            padding: isTablet ? 12 : 8,
            maxHeight: isTablet ? 400 : 300,
            width: isTablet ? '80%' : '90%',
            alignSelf: 'center'
          }}>
            <FlatList
              data={packingLists}
              keyExtractor={(item) => item._id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={{
                    padding: isTablet ? 20 : 16,
                    borderBottomWidth: 1,
                    borderBottomColor: '#EEEEEE',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  }}
                  onPress={() => {
                    // Haptic feedback will be implemented when the library is available
                    // Haptics.selectionAsync();
                    setSelectedPackingList(item);
                    setShowPackingListDropdown(false);
                  }}
                >
                  <DropdownText>{item.name}</DropdownText>
                  <Checkbox
                    checked={selectedPackingList?._id === item._id}
                    onToggle={() => {
                      setSelectedPackingList(item);
                      setShowPackingListDropdown(false);
                    }}
                    size={isTablet ? 24 : 20}
                  />
                </TouchableOpacity>
              )}
            />
          </View>
        </Modal>
      )}

      {/* Success Modal for Outfit Save/Update */}
      <GenericSuccessModal
        isVisible={showSuccessModal}
        onClose={() => {
          setShowSuccessModal(false);
          onClose(); // Close the main modal after success
        }}
        emoji="✨"
        title="Outfit Saved!"
        message={`Your outfit "${successModalData.outfitName}" has been successfully ${successModalData.isUpdate ? 'updated' : 'created'} with ${successModalData.itemCount} items.`}
        showPoints={false}
        buttonText="Done"
      />
    </Modal>
  );
}

// Export the component wrapped in React.memo for better performance
const MemoizedPlanOutfitModal = React.memo(PlanOutfitModal);
MemoizedPlanOutfitModal.displayName = 'PlanOutfitModal';

export default MemoizedPlanOutfitModal;
